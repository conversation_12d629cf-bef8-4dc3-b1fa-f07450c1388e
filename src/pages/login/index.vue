<route lang="jsonc" type="page">
{
  "layout": "default",
  "needLogin": false,
  "style": {
    "navigationBarTitleText": "登录",
    "navigationStyle": "custom"
  }
}
</route>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'
import { useUserStore } from '@/store/user'
import { toast } from '@/utils/toast'

const userStore = useUserStore()

const message = useMessage()

const VITE_APP_TITLE = import.meta.env.VITE_APP_TITLE
const VITE_APP_VERSION = import.meta.env.VITE_APP_VERSION || 'APP_VERSION'

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
})

// 加载状态
const loading = ref(false)

function handleClear() {
  uni.setStorageSync('loginForm', loginForm.value)
}

// 登录处理
async function handleLogin() {
  uni.setStorageSync('loginForm', loginForm.value)
  // 表单验证
  if (!loginForm.value.username.trim()) {
    toast.error('请输入账号')
    return
  }

  if (!loginForm.value.password.trim()) {
    toast.error('请输入密码')
    return
  }

  loading.value = true

  try {
    // 构造登录参数，不需要验证码时传空值
    const loginData = {
      username: loginForm.value.username,
      password: loginForm.value.password,
      code: '',
      uuid: '',
      loginType: 'app',
    }

    const res = await userStore.login(loginData)
    if (res.code === 200) {
      uni.reLaunch({
        url: '/pages/index/index',
      })
    }
    else {
      message.alert({
        title: '登录失败',
        msg: res.msg,
      })
    }
  }
  catch (error) {
    message.alert({
      title: '登录失败',
      msg: '登录失败，请检查账号密码',
    })
  }
  finally {
    loading.value = false
  }
}

onMounted(() => {
  const savedForm = uni.getStorageSync('loginForm')
  console.log(savedForm)
  if (savedForm) {
    loginForm.value = savedForm
  }
})
</script>

<template>
  <view class="global-bg-image-bg" h-screen flex items-center flex-justify-center overflow-hidden>
    <wd-card
      custom-class="w-327px border-rd-15px !bg-[#F2F9FA]"
      custom-content-class="text-black"
    >
      <!-- header -->
      <view border-b="#004166" align-center flex items-center gap-12px border-b-1px border-b-solid py-15px>
        <image src="/static/logo.svg" w="38px" h="38px" />
        <text flex-1 text-42rpx color="#004166">
          走线架AI
        </text>
        <view flex items-end gap-5px>
          <svg xmlns="http://www.w3.org/2000/svg" width="6" height="6" viewBox="0 0 6 6" fill="none">
            <circle cx="3" cy="3" r="2.5" fill="#004166" stroke="#004166" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" width="6" height="6" viewBox="0 0 6 6" fill="none">
            <circle cx="3" cy="3" r="2.75" stroke="#004166" stroke-width="0.5" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" width="6" height="6" viewBox="0 0 6 6" fill="none">
            <circle cx="3" cy="3" r="3" fill="#90C31F" />
          </svg>
        </view>
      </view>

      <!-- content -->
      <view mb-12px mt-16px text-46rpx>
        <text color="#004166">
          {{ VITE_APP_TITLE }}
        </text>
      </view>

      <!-- content: input bar -->
      <view>
        <view mb-4px mt-12px>
          <text color="#004166">
            登录账号
          </text>
        </view>
        <wd-input
          v-model="loginForm.username"
          placeholder="请输入账号"
          :disabled="loading"
          :clearable="true"
          :no-border="true"
          p-5px
          border="1px solid #c8cbcc rd-5px"
          @clear="handleClear"
        />
      </view>

      <!-- content: input bar -->
      <view mt-12px>
        <view mb-4px mt-12px>
          <text mb-4px color="#004166">
            登录密码
          </text>
        </view>
      </view>
      <wd-input
        v-model="loginForm.password"
        placeholder="请输入登录密码"
        :show-password="true"
        :disabled="loading"
        :clearable="true"
        :no-border="true"
        p-5px
        border="1px solid #c8cbcc rd-5px"
        @clear="handleClear"
      />

      <view flex justify-center>
        <image src="/static/images/map.svg" w="320px" h="240px" my-5px />
      </view>

      <!-- content: version -->
      <view my-5px text-center>
        <text>版本号: {{ VITE_APP_VERSION }}</text>
      </view>

      <!-- footer -->
      <view mb-16px>
        <wd-button
          block
          type="primary"
          :loading="loading"
          :disabled="loading"
          @click="handleLogin"
        >
          {{ loading ? '登录中...' : '登录' }}
        </wd-button>
      </view>
    </wd-card>
  </view>
</template>

<style lang="scss" scoped>
// 无需额外样式，使用内联样式
:deep(.wd-icon.wd-input__icon.wd-icon-eye-close) {
  background-color: transparent;
}
.wd-input {
}
</style>
