<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "工单详情"
  }
}
</route>

<script lang="ts" setup>
import { useQuery } from '@tanstack/vue-query'
import { projectDelete, projectDetail, projectUpdate } from '@/api/business'
import CollapsePanelItem from '@/components/CollapsePanelItem/CollapsePanelItem.vue'
import UploadItem from '@/components/UploadItem/UploadItem.vue'
import { getComplianceByNoQualified, getExistTagType, getRoomTagType, getStatusTagType } from '@/utils/getTagType'

defineOptions({
  name: 'Detail',
})

interface IShowPanelConfig {
  [key: string]: ProjectStatus[]
}

type IShowBtnConfig = IShowPanelConfig

const uploadItemRef = ref<InstanceType<typeof UploadItem> | null>(null)

// 获取页面参数
const projectId = ref<string>('')

const isReUpload = ref<boolean>(false)

// 页面加载时获取参数
onLoad((options) => {
  if (options.id) {
    projectId.value = options.id
  }
})

// 使用useQuery获取项目详情
const {
  data: projectDetailData,
  isLoading,
  error,
  refetch,
} = useQuery<ProjectDetailResponse>({
  queryKey: ['projectDetail', projectId],
  queryFn: async () => {
    if (!projectId.value) {
      throw new Error('项目ID不能为空')
    }
    const res = await projectDetail(projectId.value)
    return res?.result
  },
  enabled: computed(() => !!projectId.value),
})

const value = ref<string[]>([
  '项目信息',
  '图片/视频上传',
  '整个项目结果展示',
  '单张图片结果展示',
  '用户申诉',
])

// 编辑模式状态
const isEditMode = ref<boolean>(false)

// 整个项目识别结果数据
const recognize = computed(() => projectDetailData.value?.projectRecognizeResultRes)

function formatMM(num?: number) {
  return typeof num === 'number' ? `${num}mm` : '-'
}

function toStr(v?: number | string) {
  return (v ?? '-') as string
}

function handleEdit() {
  console.log('编辑操作')
  uni.navigateTo({
    url: `/pages/newproject/index?id=${projectId.value}`,
  })
}

function handleDelete() {
  if (!projectId.value) {
    uni.showToast({
      title: '项目ID不存在',
      icon: 'error',
    })
    return
  }

  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个项目吗？删除后无法恢复。',
    confirmText: '删除',
    cancelText: '取消',
    confirmColor: '#ff4757',
    success: (res) => {
      if (res.confirm) {
        // 用户确认删除
        performDelete()
      }
    },
  })
}

// 执行删除操作
async function performDelete() {
  try {
    const res = await projectDelete(projectId.value)
    if (res.code === 200) {
      uni.showToast({
        title: '删除成功',
        icon: 'success',
      })
      // 触发项目列表刷新事件
      uni.$emit('projectAdded')
      // 返回上一页
      setTimeout(() => {
        uni.navigateBack({
          delta: 1,
        })
      }, 1500)
    }
    else {
      throw new Error(res.message || '删除失败')
    }
  }
  catch (error) {
    console.error('删除项目失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'error',
    })
  }
}

function handlePreviewImg(imgUrl: string) {
  uni.previewImage({
    urls: [imgUrl],
  })
}

const ShowPanelConfig: IShowPanelConfig = {
  '图片/视频上传': ['图像待提交'],
  '整个项目结果展示': ['AI识别中', 'AI识别待确认', '已结束'],
  '单张图片结果展示': ['AI识别中', 'AI识别待确认', '已结束'],
  // '用户申诉': ['申诉待审批', '申诉已驳回', '申诉已通过', '已结束'],
}

function isShowPanel(panelName: string) {
  // 编辑模式优先判断，当编辑模式时视为 '图像待提交' 状态
  const currentStatus = isEditMode.value
    ? '图像待提交'
    : projectDetailData.value?.projectInfoRes?.projectStatus

  return !!ShowPanelConfig[panelName]?.includes(currentStatus)
}

const ShowBtnConfig: IShowBtnConfig = {
  图像待提交: ['图像待提交'],
  AI识别待确认: ['AI识别待确认'],
  // ['图像待提交', 'AI识别待确认'],
  申诉待审批: ['申诉待审批'],
}

function isShowBtn(btnGroupName: string) {
  // 编辑模式优先判断，当编辑模式时视为 '图像待提交' 状态
  const currentStatus = isEditMode.value
    ? '图像待提交'
    : projectDetailData.value?.projectInfoRes?.projectStatus

  return !!ShowBtnConfig[btnGroupName]?.includes(currentStatus)
}

function handleSpecification() {
  console.log('图像上传规范')
}

function handleSaveUpload() {
  uploadItemRef.value?.onUploadClick('保存')
}

function handleSubmitUpload() {
  uploadItemRef.value?.onUploadClick('保存并提交')
}

function handleReUpload() {
  isReUpload.value = true
  // 切换到编辑模式
  isEditMode.value = true
  uni.showToast({
    title: '已进入编辑模式',
    icon: 'success',
  })
}

function handleClick(flag: string) {
  switch (flag) {
    case '保存':
      handleSaveUpload()
      break
    case '保存并提交':
      handleSubmitUpload()
      break
    case '图片重新上传':
      handleReUpload()
      break
    case '同意':
      console.log('同意')
      break
    case '任务结束':
      handleFinish()
      break
    case '驳回':
      console.log('驳回')
      break
    default:
      break
  }
}

function handleFinish() {
  if (!projectDetailData.value?.projectInfoRes) {
    uni.showToast({ title: '项目信息不存在', icon: 'error' })
    return
  }

  uni.showModal({
    title: '确认结束任务',
    content: '确定要将任务设置为“已结束”并返回吗？',
    confirmText: '是',
    cancelText: '否',
    success: async (res) => {
      if (res.confirm) {
        try {
          const payload: ProjectInfoItem = {
            id: projectDetailData.value.projectInfoRes.id,
            projectStatus: '已结束',
          }
          const r = await projectUpdate(payload)
          if (r.code === 200) {
            uni.showToast({ title: '已结束', icon: 'success' })
            setTimeout(() => {
              uni.navigateBack({ delta: 1 })
            }, 800)
          }
          else {
            throw new Error(r.message || '更新失败')
          }
        }
        catch (e) {
          console.error(e)
          uni.showToast({ title: '操作失败', icon: 'error' })
        }
      }
    },
  })
}

function handleRefreshList() {
  refetch()
}

onMounted(() => {
  uni.$on('projectEdited', handleRefreshList)
})

onUnmounted(() => {
  uni.$off('projectEdited', handleRefreshList)
})
</script>

<template>
  <view class="" p-t-16rpx pb-80px>
    <!-- 加载状态 -->
    <view v-if="isLoading" flex="~ col items-center justify-center" min-h-400px>
      <wd-loading size="40rpx" />
      <text m-t-20rpx text-28rpx color="#999">加载中...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" flex="~ col items-center justify-center" min-h-400px>
      <text text-28rpx color="#f56c6c">加载失败</text>
      <wd-button type="primary" size="small" m-t-20rpx @click="refetch">
        重试
      </wd-button>
    </view>

    <!-- 项目详情内容 - 完全加载后才显示 -->
    <template v-else-if="projectDetailData">
      <wd-collapse v-model="value" m-x-16rpx custom-class="!bg-transparent">
        <!-- 项目信息 -->
        <CollapsePanelItem title="项目信息" name="项目信息">
          <template #header-tools>
            <view flex gap-10px>
              <wd-button type="warning" :round="false" size="small" @click.stop="handleEdit">
                编辑
              </wd-button>
              <wd-button type="error" :round="false" size="small" @click.stop="handleDelete">
                删除
              </wd-button>
            </view>
          </template>
          <!-- 项目详情内容 -->
          <view>
            <LabelValue label="机房名称" :value="projectDetailData.projectInfoRes?.roomName" />
            <LabelValue label="编号" :value="projectDetailData.projectInfoRes?.roomCode" />
            <LabelValue label="机房地址" :value="projectDetailData.projectInfoRes?.roomAddress" />
            <LabelValue label="机房类型">
              <wd-tag :type="getRoomTagType(projectDetailData.projectInfoRes?.roomType || '')" plain>
                {{ projectDetailData.projectInfoRes?.roomType }}
              </wd-tag>
            </LabelValue>
            <LabelValue label="项目状态">
              <wd-tag :type="getStatusTagType(projectDetailData.projectInfoRes?.projectStatus || '')" plain>
                {{ projectDetailData.projectInfoRes?.projectStatus }}
              </wd-tag>
            </LabelValue>
            <LabelValue label="框架名称" :value="projectDetailData.projectInfoRes?.frameName" />
            <LabelValue label="执行地市" :value="projectDetailData.projectInfoRes?.city" />
            <LabelValue label="施工单位" :value="projectDetailData.projectInfoRes?.buildUnit" />
            <LabelValue label="区公司机房建设负责人" :value="projectDetailData.projectInfoRes?.districtManager" />
            <LabelValue label="地市机房建设负责人" :value="projectDetailData.projectInfoRes?.cityManager" />
            <LabelValue label="监理单位" :value="projectDetailData.projectInfoRes?.supervisionUnit" />
            <LabelValue label="监理员" :value="projectDetailData.projectInfoRes?.supervisor" />
            <LabelValue label="施工队长" :value="projectDetailData.projectInfoRes?.buildLeader" />
            <LabelValue label="收集完成时限" :value="projectDetailData.projectInfoRes?.finishTime" />
            <LabelValue label="创建人" :value="projectDetailData.projectInfoRes?.createBy" />
            <LabelValue label="创建时间" :value="projectDetailData.projectInfoRes?.createTime" />
          </view>
        </CollapsePanelItem>

        <!-- 图片/视频上传 -->
        <CollapsePanelItem v-if="isShowPanel('图片/视频上传')" title="图片/视频上传" name="图片/视频上传">
          <template #header-tools>
            <view flex gap-10px>
              <!-- <wd-button type="info" :round="false" plain size="small" @click.stop="handleSpecification">
                图像上传规范
              </wd-button> -->
            </view>
          </template>
          <view>
            <UploadItem
              ref="uploadItemRef"
              :project-id="projectId"
              :project-detail-data="projectDetailData?.projectAttachResList || projectDetailData?.recognizeDetailResList || []"
              :is-re-upload="isReUpload"
              @refresh="refetch"
            />
          </view>
        </CollapsePanelItem>

        <!-- 整个项目结果展示 -->
        <CollapsePanelItem v-if="isShowPanel('整个项目结果展示')" title="整个项目结果展示" name="整个项目结果展示">
          <template v-if="recognize">
            <Card size="small">
              <template #title>
                累计部件识别结果
              </template>
              <view p-12rpx>
                <LabelValue first:mt-0 label="外观">
                  <wd-tag :type="getExistTagType(recognize?.appearance)">
                    {{ toStr(recognize?.appearance) }}
                  </wd-tag>
                </LabelValue>
                <LabelValue first:mt-0 label="走线架层数" :value="toStr(recognize?.cableRackLevel)" />

                <view flex justify-between>
                  <LabelValue first:mt-0 label="立柱">
                    <wd-tag :type="getExistTagType(recognize?.pillarStatus)">
                      {{ toStr(recognize?.pillarStatus) }}
                    </wd-tag>
                  </LabelValue>
                  <LabelValue first:mt-0 label="合格数量" :value="toStr(recognize?.pillarPassCount)" />
                </view>

                <view flex justify-between>
                  <LabelValue first:mt-0 label="龙骨间横担">
                    <wd-tag :type="getExistTagType(recognize?.crossArmStatus)">
                      {{ toStr(recognize?.crossArmStatus) }}
                    </wd-tag>
                  </LabelValue>
                  <LabelValue first:mt-0 label="合格数量" :value="toStr(recognize?.crossArmPassCount)" />
                </view>

                <view flex justify-between>
                  <LabelValue first:mt-0 label="吊挂">
                    <wd-tag :type="getExistTagType(recognize?.hangerStatus)">
                      {{ toStr(recognize?.hangerStatus) }}
                    </wd-tag>
                  </LabelValue>
                  <LabelValue first:mt-0 label="合格数量" :value="toStr(recognize?.hangerPassCount)" />
                  <LabelValue first:mt-0 label="不合格数量" :value="toStr(recognize?.hangerFailCount)" />
                </view>

                <view flex justify-between>
                  <LabelValue first:mt-0 label="接地">
                    <wd-tag :type="getExistTagType(recognize?.groundingStatus)">
                      {{ toStr(recognize?.groundingStatus) }}
                    </wd-tag>
                  </LabelValue>
                  <LabelValue first:mt-0 label="合格数量" :value="toStr(recognize?.groundingPassCount)" />
                  <LabelValue first:mt-0 label="不合格数量" :value="toStr(recognize?.groundingFailCount)" />
                </view>
              </view>
            </Card>

            <Card size="small">
              <template #title>
                累计参数测量结果
              </template>
              <view p-12rpx>
                <LabelValue first:mt-0 label="走线架长度" :value="formatMM(recognize?.cableRackLong)" />
                <LabelValue first:mt-0 label="吊挂平均间距" :value="formatMM(recognize?.hangerSpacing)" />
                <LabelValue first:mt-0 label="横担平均间距" :value="formatMM(recognize?.crossArmSpacing)" />
                <LabelValue first:mt-0 label="立柱平均间距" :value="formatMM(recognize?.pillarSpacing)" />
              </view>
            </Card>

            <Card size="small">
              <template #title>
                综合结果
              </template>
              <view p-12rpx>
                <LabelValue first:mt-0 label="识别结果">
                  <wd-tag :type="getExistTagType(recognize?.result)">
                    {{ toStr(recognize?.result) }}
                  </wd-tag>
                </LabelValue>
                <LabelValue first:mt-0 label="不规范点">
                  <view white-space-pre-wrap>
                    {{ toStr(recognize?.noQualified) }}
                  </view>
                </LabelValue>
              </view>
            </Card>

            <!-- <Card size="small">
              <template #title>
                申诉结果
              </template>
              <view p-12rpx>
                <LabelValue label="申诉结果">
                  <wd-tag type="success">
                    符合规范
                  </wd-tag>
                </LabelValue>
              </view>
            </Card> -->
          </template>
          <view v-if="!recognize" p-32rpx text-center color-gray-400>
            暂无整体识别结果
          </view>
        </CollapsePanelItem>

        <!-- 单张图片结果展示 -->
        <CollapsePanelItem v-if="isShowPanel('单张图片结果展示')" title="单张图片结果展示" name="单张图片结果展示" theme="success">
          <view v-for="(detail, index) in projectDetailData.recognizeDetailResList" :key="detail.id || index" mb-16rpx>
            <!-- 基本信息 -->
            <view mb-12rpx bg-gray-50 p-12rpx>
              <LabelValue label="上传人员" :value="toStr(detail.uploadBy)" />
              <LabelValue label="识别编号" :value="toStr(detail.recognizeNo)" />
              <LabelValue label="识别时间" :value="toStr(detail.recognizeTime)" />
              <LabelValue label="关联图像">
                <view v-if="detail.attachPath" h-120rpx w-120rpx flex items-center justify-center bg-gray-200>
                  <image :src="detail.attachPath" h-full w-full object-cover @click="handlePreviewImg(detail.attachPath)" />
                </view>
                <text v-else color-gray-400>暂无图像</text>
              </LabelValue>
              <LabelValue label="识别状态">
                <wd-tag :type="getExistTagType(detail.status)">
                  {{ toStr(detail.status) }}
                </wd-tag>
              </LabelValue>
            </view>

            <!-- 单张部件识别结果 -->
            <Card size="small" theme="success">
              <template #title>
                单张部件识别结果
              </template>
              <view p-12rpx>
                <LabelValue label="外观">
                  <wd-tag :type="getExistTagType(detail.appearance)">
                    {{ toStr(detail.appearance) }}
                  </wd-tag>
                </LabelValue>
                <LabelValue label="走线架层数" :value="toStr(detail.cableRackLevel)" />

                <view flex justify-between>
                  <LabelValue label="立柱">
                    <wd-tag :type="getExistTagType(detail.pillarStatus)">
                      {{ toStr(detail.pillarStatus) }}
                    </wd-tag>
                  </LabelValue>
                  <LabelValue label="合格数量" :value="toStr(detail.pillarPassCount)" />
                </view>

                <view flex justify-between>
                  <LabelValue label="龙骨间横担">
                    <wd-tag :type="getExistTagType(detail.crossArmStatus)">
                      {{ toStr(detail.crossArmStatus) }}
                    </wd-tag>
                  </LabelValue>
                  <LabelValue label="合格数量" :value="toStr(detail.crossArmPassCount)" />
                </view>

                <view flex justify-between>
                  <LabelValue label="吊挂">
                    <wd-tag :type="getExistTagType(detail.hangerStatus)">
                      {{ toStr(detail.hangerStatus) }}
                    </wd-tag>
                  </LabelValue>
                  <LabelValue label="合格数量" :value="toStr(detail.hangerPassCount)" />
                  <LabelValue label="不合格数量" :value="toStr(detail.hangerFailCount)" />
                </view>

                <view flex justify-between>
                  <LabelValue label="接地">
                    <wd-tag :type="getExistTagType(detail.groundingStatus)">
                      {{ toStr(detail.groundingStatus) }}
                    </wd-tag>
                  </LabelValue>
                  <LabelValue label="合格数量" :value="toStr(detail.groundingPassCount)" />
                  <LabelValue label="不合格数量" :value="toStr(detail.groundingFailCount)" />
                </view>

                <LabelValue label="中间龙骨">
                  <wd-tag :type="getExistTagType(detail.keelStatus)">
                    {{ toStr(detail.keelStatus) }}
                  </wd-tag>
                </LabelValue>
              </view>
            </Card>

            <!-- 单张参数测量结果 -->
            <Card size="small" theme="success">
              <template #title>
                单张参数测量结果
              </template>
              <view p-12rpx>
                <LabelValue label="走线架长度" :value="formatMM(detail.cableRackLong)" />
                <LabelValue label="吊挂平均间距" :value="formatMM(detail.hangerSpacing)" />
                <LabelValue label="横担平均间距" :value="formatMM(detail.crossArmSpacing)" />
                <LabelValue label="立柱平均间距" :value="formatMM(detail.pillarSpacing)" />
              </view>
            </Card>
          </view>

          <!-- 如果没有识别详情数据 -->
          <view v-if="!projectDetailData.recognizeDetailResList?.length" p-32rpx text-center color-gray-400>
            暂无单张图片识别结果
          </view>
        </CollapsePanelItem>

        <!-- 用户申诉 -->
        <CollapsePanelItem v-if="isShowPanel('用户申诉')" title="用户申诉" name="用户申诉">
          用户申诉内容
        </CollapsePanelItem>
      </wd-collapse>

      <view shadow="0 -2px 8px rgba(0,0,0,0.1)" fixed bottom-0 left-0 right-0 z-100 bg-white p-16px>
        <!-- 图像待提交 -->
        <view v-if="isShowBtn('图像待提交')">
          <view flex="~" gap-20rpx>
            <wd-button type="success" :round="false" plain block flex-1 @click="handleClick('保存')">
              保存
            </wd-button>
            <wd-button type="success" :round="false" block flex-1 @click="handleClick('保存并提交')">
              保存并提交
            </wd-button>
          </view>
        </view>
        <!-- AI识别待确认 -->
        <template v-else-if="isShowBtn('AI识别待确认')">
          <view flex="~" gap-20rpx>
            <wd-button type="primary" :round="false" flex-1 @click="handleClick('图片重新上传')">
              图片重新上传
            </wd-button>
            <!-- <wd-button type="warning" :round="false" flex-1 @click="handleClick('用户申诉')">
              用户申诉
            </wd-button> -->
            <wd-button type="success" :round="false" flex-1 @click="handleClick('任务结束')">
              任务结束
            </wd-button>
          </view>
        </template>
        <!-- 申诉待审批 -->
        <view v-else-if="isShowBtn('申诉待审批')">
          <view flex="~" gap-20rpx>
            <wd-button type="error" :round="false" block flex-1 @click="handleClick('驳回')">
              驳回
            </wd-button>
            <wd-button type="success" :round="false" block flex-1 @click="handleClick('同意')">
              同意
            </wd-button>
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
