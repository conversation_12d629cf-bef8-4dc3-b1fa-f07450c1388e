<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "新增项目"

  }
}
</route>

<script lang="ts" setup>
import { useQuery } from '@tanstack/vue-query'
import { projectAdd, projectDetail, projectUpdate } from '@/api/business'

// 页面参数
const projectId = ref<string>('')
const isEditMode = ref<boolean>(false)

// 页面标题和按钮文字
const pageTitle = computed(() => isEditMode.value ? '编辑项目' : '新增项目')
const submitButtonText = computed(() => isEditMode.value ? '更新' : '提交')

const value = ref<string[]>(['项目信息'])
const defaultValue = ref<number>(Date.now())
const form = ref()
const formData = ref({
  id: '',
  roomName: '',
  roomCode: '',
  roomAddress: '',
  roomType: '',
  frameName: '',
  city: '',
  districtManager: '',
  cityManager: '',
  supervisionUnit: '',
  supervisor: '',
  buildUnit: '',
  buildLeader: '',
  finishTime: '',
})

// 页面加载时获取参数
onLoad((options) => {
  if (options.id) {
    projectId.value = options.id
    isEditMode.value = true
    value.value = ['项目信息编辑']
    // 动态设置页面标题
    uni.setNavigationBarTitle({
      title: '编辑项目',
    })
  }
})

// 使用useQuery获取项目详情（编辑模式时）
const {
  data: projectDetailData,
  isLoading: isLoadingDetail,
  error: detailError,
} = useQuery({
  queryKey: ['projectDetail', projectId],
  queryFn: async () => {
    if (!projectId.value)
      return null
    const res = await projectDetail(projectId.value)
    return res?.result
  },
  enabled: computed(() => isEditMode.value && !!projectId.value),
})

// 监听详情数据变化，回显到表单
watch(projectDetailData, (data) => {
  if (data?.projectInfoRes) {
    const info = data.projectInfoRes
    formData.value = {
      id: info.id || '',
      roomName: info.roomName || '',
      roomCode: info.roomCode || '',
      roomAddress: info.roomAddress || '',
      roomType: info.roomType || '',
      frameName: info.frameName || '',
      city: info.city || '',
      districtManager: info.districtManager || '',
      cityManager: info.cityManager || '',
      supervisionUnit: info.supervisionUnit || '',
      supervisor: info.supervisor || '',
      buildUnit: info.buildUnit || '',
      buildLeader: info.buildLeader || '',
      finishTime: info.finishTime || '',
    }
  }
}, { immediate: true })

const roomTypeList = ref([
  {
    label: '生产楼',
    value: '生产楼',
  },
  {
    label: '汇聚机房',
    value: '汇聚机房',
  },
])

const districtList = ref([
  {
    label: '张三',
    value: '张三',
    id: 1,
  },
  {
    label: '张三2',
    value: '张三2',
    id: 2,
  },

])

const cityManagerList = ref([
  {
    label: '李四',
    value: '李四',
    id: 1,
  },
  {
    label: '李四2',
    value: '李四2',
    id: 2,
  },
])
function formatDateTime(date) {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hour = String(d.getHours()).padStart(2, '0')
  const min = String(d.getMinutes()).padStart(2, '0')
  const sec = String(d.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hour}:${min}:${sec}`
}

async function handleSubmit() {
  const params = {
    ...formData.value,
  }
  params.finishTime = formatDateTime(params.finishTime)

  form.value.validate()
    .then(({ valid }) => {
      if (valid) {
        console.log(params, 'params表单数据')

        // 根据模式选择不同的API
        const apiCall = isEditMode.value ? projectUpdate(params) : projectAdd(params)
        const successMessage = isEditMode.value ? '更新成功' : '提交成功'

        apiCall.then((res) => {
          if (res.code === 200) {
            uni.showToast({
              title: successMessage,
              icon: 'success',
            })
            if (!isEditMode.value) {
              // 触发项目列表刷新事件
              uni.$emit('projectAdded')
            }
            else {
              uni.$emit('projectEdited')
            }
            uni.navigateBack({
              delta: 1,
            })
          }
        }).catch((error) => {
          console.error('提交失败:', error)
          uni.showToast({
            title: isEditMode.value ? '更新失败' : '提交失败',
            icon: 'error',
          })
        })
      }
    })
    .catch((error) => {
      console.log(error, 'error')
    })
}
function handleCancel() {
  uni.navigateBack({
    delta: 1,
  })
}
const rules = {

}
</script>

<template>
  <view pb="150rpx" min-h-screen>
    <!-- 加载状态 -->
    <view v-if="isEditMode && isLoadingDetail" flex="~ col items-center justify-center" min-h-200px>
      <wd-loading size="40rpx" />
      <text m-t-20rpx text-28rpx color="#999">加载中...</text>
    </view>

    <!-- 表单内容 -->
    <wd-collapse v-else v-model="value" mx-16rpx>
      <wd-collapse-item :title="isEditMode ? '项目信息编辑' : '项目信息新增'" :name="value[0]">
        <wd-form ref="form" :model="formData" :rules="rules">
          <wd-cell-group>
            <view mt-16rpx>
              <text text-sm>机房名称：</text>
              <wd-input
                v-model="formData.roomName"
                prop="roomName"
                placeholder="请输入内容"
                :rules="[{ required: true, message: '请填写机房名称' }]"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>编号：</text>
              <wd-input
                v-model="formData.roomCode"
                prop="roomCode"
                placeholder="请输入内容"
                :rules="[{ required: true, message: '请填写编号' }]"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>机房地址：</text>
              <wd-input
                v-model="formData.roomAddress"
                prop="roomAddress"
                placeholder="请输入内容"
                :rules="[{ required: true, message: '请填写机房地址' }]"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>机房类型：</text>
              <wd-picker
                v-model="formData.roomType"
                :columns="roomTypeList"
                placeholder="请选择内容"
                prop="roomType"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>框架名称：</text>
              <wd-input
                v-model="formData.frameName"
                prop="frameName"
                placeholder="请输入内容"
                :rules="[{ required: true, message: '请填写框架名称' }]"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>机房所属地市公司：</text>
              <wd-input
                v-model="formData.city"
                prop="city"
                placeholder="请输入内容"
                :rules="[{ required: true, message: '请填写机房所属地市公司' }]"
              />
            </view>
            <view mt-16rpx flex justify-between gap-14rpx>
              <view flex="1" mr-10rpx>
                <text text-sm>区公司机房建设负责人：</text>
                <wd-picker
                  v-model="formData.districtManager"
                  prop="districtManager"
                  :columns="districtList"
                  placeholder="请选择内容"
                  required
                />
              </view>
              <view flex="1" ml="10rpx">
                <text text-sm>地市机房建设负责人：</text>
                <wd-picker
                  v-model="formData.cityManager"
                  prop="cityManager"
                  :columns="cityManagerList"
                  placeholder="请选择内容"
                />
              </view>
            </view>
            <view mt-16rpx>
              <text text-sm>监理单位：</text>
              <wd-input
                v-model="formData.supervisionUnit"
                prop="supervisionUnit"
                placeholder="请输入内容"
                :rules="[{ required: true, message: '请填写监理单位' }]"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>监理员：</text>
              <wd-input
                v-model="formData.supervisor"
                prop="supervisor"
                placeholder="请输入内容"
                :rules="[{ required: true, message: '请填写监理员' }]"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>施工单位：</text>
              <wd-input
                v-model="formData.buildUnit"
                prop="buildUnit"
                placeholder="请输入内容"
                :rules="[{ required: true, message: '请填写施工单位' }]"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>施工队长：</text>
              <wd-input
                v-model="formData.buildLeader"
                prop="buildLeader"
                placeholder="请输入内容"
                :rules="[{ required: true, message: '请填写施工队长' }]"
              />
            </view>
            <view mt-16rpx>
              <text text-sm>收集完成期限：</text>
              <wd-datetime-picker
                v-model="formData.finishTime"
                :default-value="defaultValue"
                placeholder="请选择时间"
                type="datetime"
                use-second
                required
              />
            </view>
          </wd-cell-group>
          <view class="footer">
            <view flex justify-around>
              <wd-button type="info" size="large" block @click="handleCancel">
                取消
              </wd-button>
              <wd-button type="success" size="large" block @click="handleSubmit">
                {{ submitButtonText }}
              </wd-button>
            </view>
          </view>
        </wd-form>
      </wd-collapse-item>
    </wd-collapse>
  </view>
</template>

<style lang="scss" scoped>
:deep(.wd-input.is-cell),
:deep(.wd-cell),
:deep(.wd-cell__wrapper) {
  padding: 6rpx 0;
}
:deep(.wd-collapse-item__title::after) {
  content: '';
  position: absolute;
  top: 22rpx;
  left: 0;
  width: 4rpx;
  height: 50rpx;
  background-color: #0085d0;
}

:deep(.wd-collapse-item__header.is-expanded),
:deep(.wd-collapse-item__header) {
  padding: 0;
  padding: 20rpx;
  border-bottom: 4rpx solid #0085d080;
}
:deep(.wd-collapse-item__body) {
  padding: 10rpx;
}
:deep(.wd-input__value),
:deep(.wd-cell__wrapper) {
  border: 2rpx solid #c8cbcc;
  padding: 10rpx;
  max-height: 46rpx;
  min-height: 46rpx;
  border-radius: 10rpx;
}
:deep(.wd-input::after) {
  height: 0;
}
.footer {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: #fff;
  padding: 20rpx 0;
  padding-bottom: calc(24rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  .wd-button {
    width: 45%;
    border-radius: 10rpx;
  }
}
</style>
