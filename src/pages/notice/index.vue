<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "通知中心"
  }
}
</route>

<script lang="ts" setup>
import { getNoticeList, markNoticeAsRead } from '@/api/notice'

defineOptions({
  name: 'Notice',
})

// 通知列表数据
const noticeList = ref<NoticeItem[]>([])
const loading = ref(false)

// 获取通知类型对应的颜色主题
function getNoticeTheme(noticeType?: NoticeType) {
  switch (noticeType) {
    case '1': // 任务通知
      return {
        color: 'rgba(0, 133, 208, 1)',
        backgroundColor: 'rgba(0, 133, 208, 0.05)',
        borderColor: 'rgba(0, 133, 208, 0.3)',
      }
    case '2': // 审批通知
      return {
        color: 'rgb(214, 146, 81)',
        backgroundColor: 'rgba(214, 146, 81, 0.05)',
        borderColor: 'rgba(214, 146, 81, 0.3)',
      }
    case '3': // 结果通知
      return {
        color: '#3DCCCC',
        backgroundColor: '#3DCCCC0D',
        borderColor: '#3DCCCC80',
      }
    default:
      return {
        color: 'rgba(0, 133, 208, 1)',
        backgroundColor: 'rgba(0, 133, 208, 0.05)',
        borderColor: 'rgba(0, 133, 208, 0.3)',
      }
  }
}

// 获取通知类型标题
function getNoticeTypeTitle(noticeType?: NoticeType) {
  switch (noticeType) {
    case '1':
      return '任务派发通知'
    case '2':
      return '审批待办通知'
    case '3':
      return '识别结果通知'
    default:
      return '系统通知'
  }
}

// 格式化时间显示
function formatTime(timeStr?: string) {
  if (!timeStr)
    return ''
  const date = new Date(timeStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:00`
}

// 点击通知项
async function handleNoticeClick(notice: NoticeItem) {
  if (!notice.noticeId)
    return

  try {
    // 如果是未读状态，标记为已读
    if (notice.isRead === '0') {
      await markNoticeAsRead(notice.noticeId)
      // 更新本地状态
      notice.isRead = '1'
    }

    // 跳转到通知详情页
    uni.navigateTo({
      url: `/pages/detail/index?id=${notice.noticeId}`,
    })
  }
  catch (error) {
    console.error('标记通知已读失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'error',
    })
  }
}

// 获取通知列表
async function fetchNoticeList() {
  try {
    loading.value = true
    const res = await getNoticeList({
      pageNum: 1,
      pageSize: 20, // 获取更多数据用于展示
      isRead: '0',
    })

    if (res?.rows) {
      noticeList.value = res?.rows || []
    }
  }
  catch (error) {
    console.error('获取通知列表失败:', error)
    uni.showToast({
      title: '获取通知失败',
      icon: 'error',
    })
  }
  finally {
    loading.value = false
  }
}

// 页面显示时获取数据
onShow(() => {
  // fetchNoticeList()
})
</script>

<template>
  <view min-h-screen flex flex-col pt-20rpx>
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container" flex items-center justify-center p-40rpx>
      <wd-loading />
      <text class="ml-20rpx">加载中...</text>
    </view>

    <!-- 通知列表 -->
    <view v-else-if="noticeList.length > 0" class="notice-list" px-20rpx>
      <view
        v-for="notice in noticeList"
        :key="notice.noticeId"
        class="notice-item"
        mb-20rpx
        border-rd-10rpx
        p-20rpx
        :style="{
          backgroundColor: getNoticeTheme(notice.noticeType).backgroundColor,
          borderLeft: `4px solid ${getNoticeTheme(notice.noticeType).color}`,
        }"
        @click="handleNoticeClick(notice)"
      >
        <!-- 通知头部 -->
        <view class="notice-header" mb-10rpx flex items-center justify-between>
          <text class="notice-type-title" text-32rpx font-bold>
            {{ getNoticeTypeTitle(notice.noticeType) }}
          </text>
          <text class="notice-time" text-28rpx text-gray-500>
            {{ formatTime(notice.createTime) }}
          </text>
        </view>

        <!-- 通知内容 -->
        <view class="notice-content" text-30rpx text-gray-500 line-height-1.5>
          <text>{{ notice.noticeContent }}</text>
          <!-- 未读标识 -->
          <view
            v-if="notice.isRead === '0'" class="unread-dot"
            position-absolute right-15rpx top-15rpx h-12rpx w-12rpx border-rd-50% bg-red-500
          />
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else class="empty-container" flex flex-col items-center justify-center p-80rpx>
      <wd-status-tip
        image="content"
        tip="暂无通知"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
