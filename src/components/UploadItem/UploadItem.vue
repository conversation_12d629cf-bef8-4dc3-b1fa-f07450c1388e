<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": ""
  }
}
</route>

<script lang="ts" setup>
const props = defineProps({
  projectDetailData: {
    type: Array,
    default: () => ([]),
  },
  projectId: {
    type: String,
    default: '',
  },
  isReUpload: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['refresh', 'delete'])

const uploader = ref()

const urlApi = import.meta.env.VITE_SERVER_BASEURL

const fileList = ref([])

const ids = ref([])

const projectDetailData = ref([...props.projectDetailData])

watch(() => props.projectDetailData, (newVal) => {
  projectDetailData.value = Array.isArray(newVal) ? [...newVal] : []
})

function interceptSelect({ files, resolve }: { files: any[], resolve: (ok: boolean) => void }) {
  // 把“本次选择”的文件并入受控列表
  fileList.value = [
    ...fileList.value,
    ...files.map(f => ({
      ...f,
      url: f.thumb || f.path || f.url,
      status: 'success',
      percent: 0,
    })),
  ]
  // 阻止组件自己去上传
  resolve(false)
}

// async function onUploadClick(status: string) {
//   if (fileList.value.length === 0 && ids.value.length === 0 && status === '保存') {
//     uni.showToast({ title: '请操作后再保存', icon: 'none' })
//     return
//   }
//   if (status === '保存并提交') {
//     if (projectDetailData.value.length === 0 && fileList.value.length === 0) {
//       uni.showToast({ title: '请先上传图片', icon: 'none' })
//       return
//     }
//   }
//   try {
//     await new Promise((resolve, reject) => {
//       uni.uploadFile({
//         url: `${urlApi}/project/project_attach`,
//         files: fileList.value.map(f => ({
//           name: 'files', // 多文件同名 key
//           uri: f.url || f.path || f.tempFilePath,
//         })),
//         formData: {
//           projectId: props.projectId,
//           aiRecognize: status === '保存并提交',
//           isReUpload: props.isReUpload,
//           delAttachIdList: ids.value.join(','),
//         },
//         success: () => {
//           uni.showToast({ title: '保存成功', icon: 'none' })
//           fileList.value = []
//           if (status === '保存并提交') {
//             uni.switchTab({
//               url: '/pages/dashboard/index',
//             })
//           }
//           else {
//             emit('refresh')
//           }
//           resolve(true)
//         },
//         fail: (err) => {
//           console.error('上传失败', err)
//           reject(err)
//         },
//       })
//     })
//   }
//   catch (e) {
//     uni.showToast({ title: '上传失败', icon: 'none' })
//   }
// }

async function onUploadClick(status: '保存' | '保存并提交') {
  if (fileList.value.length === 0 && ids.value.length === 0 && status === '保存') {
    uni.showToast({ title: '请操作后再保存', icon: 'none' })
    return
  }
  if (status === '保存并提交') {
    if (projectDetailData.value.length === 0 && fileList.value.length === 0) {
      uni.showToast({ title: '请先上传图片', icon: 'none' })
      return
    }
  }

  const formData = {
    projectId: props.projectId,
    aiRecognize: status === '保存并提交' ? 'true' : 'false',
    isReUpload: String(!!props.isReUpload),
    delAttachIdList: ids.value.join(','),
  }

  try {
    if (fileList.value.length === 0) {
      // 无文件用request 提交
      const res = await uni.request({
        url: `${urlApi}/project/project_attach`,
        method: 'POST',
        header: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: formData,
      })
      console.log('无文件提交结果', res)
      uni.showToast({ title: '保存成功', icon: 'none' })
      ids.value = []
      if (status === '保存并提交') {
        uni.switchTab({ url: '/pages/dashboard/index' })
      }
      else {
        emit('refresh')
      }
      return
    }

    // 有文件用uploadFile
    await new Promise((resolve, reject) => {
      uni.uploadFile({
        url: `${urlApi}/project/project_attach`,
        files: fileList.value.map(f => ({
          name: 'files',
          uri: f.url || f.path || f.tempFilePath,
        })),
        formData,
        success: (res) => {
          console.log('上传成功', res)
          if (res.statusCode >= 200 && res.statusCode < 300) {
            uni.showToast({ title: '保存成功', icon: 'none' })
            fileList.value = []
            ids.value = []
            if (status === '保存并提交') {
              uni.switchTab({ url: '/pages/dashboard/index' })
            }
            else {
              emit('refresh')
            }
            resolve(true)
          }
          else {
            reject(new Error(`上传失败: ${res.statusCode}`))
          }
        },
        fail: (err) => {
          console.error('上传失败 fail:', err)
          reject(err)
        },
      })
    })
  }
  catch (e) {
    console.error('提交异常', e)
    uni.showToast({ title: '上传失败', icon: 'none' })
  }
}

function deleteFile(id: string) {
  uni.showModal({
    title: '提示',
    content: '请确定是否删除图片',
    success: async (res) => {
      if (res.confirm) {
        ids.value.push(id)
        projectDetailData.value = projectDetailData.value.filter((item: { id: string }) => item.id !== id)
      }
    },
  })
}

defineExpose({
  onUploadClick,
})
</script>

<template>
  <view class="">
    <view v-if="projectDetailData.length > 0">
      <view v-for="(item, index) in projectDetailData" :key="index" class="img-flex">
        <view flex items-center>
          <wd-img :width="80" :height="80" m-10rpx :enable-preview="true" :src="(item as { attachPath: string }).attachPath" />
          <view class="filename-text">
            {{ (item as { attachName: string }).attachName }}
          </view>
        </view>
        <view flex items-center>
          <wd-icon name="delete1" size="44rpx" color="#CD202D" @click="deleteFile((item as { id: string }).id)" />
          <wd-icon name="edit-1" size="44rpx" mx-20rpx color="#D69251" />
        </view>
      </view>
    </view>

    <wd-upload
      ref="uploader"
      v-model:file-list="fileList"
      :action="`${urlApi}/project/project_attach`"
      multiple
      name="files"
      :auto-upload="false"
      :before-upload="interceptSelect"
    >
      <template #preview-cover="{ file, index }">
        <view class="preview-cover">
          {{ file?.name || `文件${index + 1}` }}
        </view>
      </template>
    </wd-upload>
  </view>
</template>

<style lang="scss" scoped>
:deep(.wd-upload) {
  flex-direction: column;
  margin-top: 20rpx;
}
:deep(.uploader) {
  position: relative;
  // border: 2rpx solid red;
  .wd-upload__preview {
    // border: 2rpx solid red;
  }
}

.preview-cover {
  position: absolute;
  right: -400rpx;
  top: 70rpx;
  max-width: 300rpx;
  overflow: hidden;
}

.img-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 2rpx solid #d6d6d6;
  padding: 0 10rpx;
  margin-bottom: 20rpx;
}

.filename-text {
  width: 300rpx;
  font-size: 28rpx;
  line-height: 1.4;
  color: #333;
  margin-left: 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-wrap: break-word;
}
</style>
