<script lang="ts" setup>
import CollapsePanelItem from './CollapsePanelItem.vue'

defineOptions({
  name: 'CollapsePanelExample',
})

const activeNames = ref<string[]>(['basic', 'project'])

const logs = ref([
  { time: '2024-01-01 10:00', action: '创建项目' },
  { time: '2024-01-01 11:00', action: '修改配置' },
  { time: '2024-01-01 12:00', action: '保存数据' },
])

function handleEdit() {
  uni.showToast({
    title: '编辑操作',
    icon: 'none',
  })
}

function handleDelete() {
  uni.showToast({
    title: '删除操作',
    icon: 'none',
  })
}

function handleConfig() {
  uni.showToast({
    title: '配置操作',
    icon: 'none',
  })
}
</script>

<template>
  <view class="example-container">
    <wd-collapse v-model="activeNames">
      <!-- 基础用法 -->
      <CollapsePanelItem title="基础信息" name="basic">
        <view class="content-section">
          <text>这是基础信息的内容</text>
          <text>没有操作按钮的简单面板</text>
        </view>
      </CollapsePanelItem>

      <!-- 带操作按钮 -->
      <CollapsePanelItem title="项目信息" name="project">
        <template #header-tools>
          <view flex gap-10px>
            <wd-button type="warning" :round="false" size="small" @click.stop="handleEdit">
              编辑
            </wd-button>
            <wd-button type="error" :round="false" size="small" @click.stop="handleDelete">
              删除
            </wd-button>
          </view>
        </template>

        <view class="content-section">
          <view class="info-item">
            <text class="label">项目名称：</text>
            <text class="value">示例项目</text>
          </view>
          <view class="info-item">
            <text class="label">创建时间：</text>
            <text class="value">2024-01-01</text>
          </view>
          <view class="info-item">
            <text class="label">状态：</text>
            <wd-tag type="success" size="small">
              进行中
            </wd-tag>
          </view>
        </view>
      </CollapsePanelItem>

      <!-- 带单个操作按钮 -->
      <CollapsePanelItem title="系统配置" name="config">
        <template #header-tools>
          <view>
            <wd-button type="primary" :round="false" size="small" @click.stop="handleConfig">
              配置
            </wd-button>
          </view>
        </template>

        <view class="content-section">
          <text>系统配置相关内容</text>
        </view>
      </CollapsePanelItem>

      <!-- 只有内容，无操作按钮 -->
      <CollapsePanelItem title="操作日志" name="logs">
        <view class="content-section">
          <view v-for="(log, index) in logs" :key="index" class="log-item">
            <text class="log-time">{{ log.time }}</text>
            <text class="log-action">{{ log.action }}</text>
          </view>
        </view>
      </CollapsePanelItem>
    </wd-collapse>
  </view>
</template>

<style lang="scss" scoped>
.example-container {
  padding: 20rpx;
}

.content-section {
  padding: 20rpx 0;

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .label {
      color: #666;
      margin-right: 16rpx;
      min-width: 120rpx;
    }

    .value {
      color: #333;
      flex: 1;
    }
  }

  .log-item {
    display: flex;
    align-items: center;
    padding: 12rpx 0;
    border-bottom: 1px solid #f0f0f0;

    .log-time {
      color: #999;
      font-size: 24rpx;
      margin-right: 20rpx;
      min-width: 200rpx;
    }

    .log-action {
      color: #333;
      flex: 1;
    }
  }
}
</style>
