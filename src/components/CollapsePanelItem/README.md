# CollapsePanelItem 组件

一个可复用的折叠面板项组件，基于 wot-design-uni 的 wd-collapse-item 封装。

## 功能特性

- ✅ 支持自定义标题
- ✅ 支持头部工具栏插槽（操作按钮等）
- ✅ 支持默认内容插槽
- ✅ 自动处理展开/收起动画
- ✅ TypeScript 类型支持

## Props

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| title | string | ✅ | - | 面板标题 |
| name | string | ✅ | - | 面板名称，用于v-model绑定 |
| expanded | boolean | ❌ | false | 是否展开（由父组件的v-model控制） |

## 插槽

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| default | 内容区域 | - |
| header-tools | 头部工具栏区域 | { expanded: boolean } |

## 使用示例

### 基础用法

```vue
<template>
  <wd-collapse v-model="activeNames">
    <CollapsePanelItem title="基础信息" name="basic">
      这是基础信息的内容
    </CollapsePanelItem>
  </wd-collapse>
</template>

<script setup>
import CollapsePanelItem from '@/components/CollapsePanelItem/CollapsePanelItem.vue'

const activeNames = ref(['basic'])
</script>
```

### 带操作按钮

```vue
<template>
  <wd-collapse v-model="activeNames">
    <CollapsePanelItem title="项目信息" name="project">
      <template #header-tools>
        <!-- 使用者自己控制布局样式 -->
        <view flex gap-10px>
          <wd-button type="warning" size="small" @click.stop="handleEdit">
            编辑
          </wd-button>
          <wd-button type="error" size="small" @click.stop="handleDelete">
            删除
          </wd-button>
        </view>
      </template>

      <!-- 项目详细信息 -->
      <view class="project-info">
        <text>项目名称：示例项目</text>
        <text>创建时间：2024-01-01</text>
      </view>
    </CollapsePanelItem>
  </wd-collapse>
</template>
```

### 多个面板

```vue
<template>
  <wd-collapse v-model="activeNames">
    <CollapsePanelItem title="基础信息" name="basic">
      基础信息内容
    </CollapsePanelItem>

    <CollapsePanelItem title="详细配置" name="config">
      <template #header-tools>
        <!-- 单个按钮也需要包装容器 -->
        <view>
          <wd-button type="primary" size="small" @click.stop="handleConfig">
            配置
          </wd-button>
        </view>
      </template>
      详细配置内容
    </CollapsePanelItem>

    <CollapsePanelItem title="操作日志" name="logs">
      操作日志内容
    </CollapsePanelItem>
  </wd-collapse>
</template>
```

## 注意事项

1. **必须在 wd-collapse 内使用**：该组件是对 wd-collapse-item 的封装
2. **事件阻止冒泡**：在 header-tools 插槽中的按钮需要使用 `@click.stop` 防止触发面板展开/收起
3. **name 属性唯一性**：确保每个面板的 name 属性在同一个 collapse 中是唯一的
4. **布局样式自控制**：header-tools 插槽不包含任何预设样式，使用者需要自己添加布局容器和样式

## 布局灵活性

组件的 header-tools 插槽完全由使用者控制布局，可以实现各种不同的排列方式：

```vue
<!-- 水平排列 -->
<template #header-tools>
  <view flex gap-10px>
    <wd-button>按钮1</wd-button>
    <wd-button>按钮2</wd-button>
  </view>
</template>

<!-- 垂直排列 -->
<template #header-tools>
  <view flex flex-col gap-5px>
    <wd-button size="small">按钮1</wd-button>
    <wd-button size="small">按钮2</wd-button>
  </view>
</template>

<!-- 右对齐 -->
<template #header-tools>
  <view flex justify-end>
    <wd-button>操作</wd-button>
  </view>
</template>

<!-- 复杂布局 -->
<template #header-tools>
  <view flex items-center gap-15px>
    <wd-tag type="success" size="small">状态</wd-tag>
    <view flex gap-8px>
      <wd-button type="primary" size="small">编辑</wd-button>
      <wd-button type="error" size="small">删除</wd-button>
    </view>
  </view>
</template>
```
