<script lang="ts" setup>
interface Props {
  /** 面板标题 */
  title: string
  /** 面板名称，用于v-model绑定 */
  name: string
  theme?: 'primary' | 'success'
}

defineOptions({
  name: 'CollapsePanelItem',
})

const props = withDefaults(defineProps<Props>(), {
  theme: 'primary',
})
</script>

<template>
  <wd-collapse-item :name="name" my-16rpx bg-white class="collapse-item" :class="[`collapse-item--${props.theme}`]">
    <template #title="{ expanded }">
      <view class="collapse-item__title" flex="~ justify-between items-center" gap-10px>
        <!-- title -->
        <text>{{ props.title }}</text>

        <!-- header-tools 插槽：用于放置操作按钮等工具 -->
        <view v-if="$slots['header-tools']" flex-1>
          <slot name="header-tools" :expanded="expanded" />
        </view>

        <!-- icon -->
        <view
          transition="transform 0.3s"
          :class="`${expanded ? 'rotate--180' : ''}`"
        >
          <wd-icon name="arrow-down" />
        </view>
      </view>
    </template>

    <!-- 默认插槽：内容区域 -->
    <slot />
  </wd-collapse-item>
</template>

<style lang="scss" scoped>
$primary-bg-color: #0085d0;
$primary-border-color: #0085d080;

$success-bg-color: #90c31f;
$success-border-color: rgba(144, 195, 31, 0.5);

.collapse-item__title::after {
  content: '';
  position: absolute;
  top: 22rpx;
  left: 0;
  width: 4rpx;
  height: 50rpx;
  // background-color: #0085d0;
}
:deep(.wd-collapse-item__header.is-expanded),
:deep(.wd-collapse-item__header) {
  padding: 0;
  padding: 20rpx;
  // border-bottom: 4rpx solid #0085d080;
}

/* theme:primary */
.collapse-item--primary {
  .collapse-item__title::after {
    background-color: $primary-bg-color;
  }
  :deep(.wd-collapse-item__header.is-expanded),
  :deep(.wd-collapse-item__header) {
    border-bottom: 4rpx solid $primary-border-color;
  }
}

/* theme:success */
.collapse-item--success {
  .collapse-item__title::after {
    background-color: $success-bg-color;
  }
  :deep(.wd-collapse-item__header.is-expanded),
  :deep(.wd-collapse-item__header) {
    border-bottom: 4rpx solid $success-border-color;
  }
}

:deep(.wd-collapse-item__body) {
  padding: 10rpx;
}
</style>
