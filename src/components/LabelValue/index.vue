<script lang="ts" setup>
defineOptions({
  name: 'LabelValue',
})

const props = withDefaults(defineProps<Props>(), {
  labelClass: '',
  valueClass: '',
})

interface Props {
  label: string
  labelClass?: string
  value?: string | number
  valueClass?: string
}
</script>

<template>
  <view class="label-value" m-t-16rpx flex text-28rpx color="#65676B">
    <view :class="props.labelClass" flex-shrink-0>
      <slot name="label">
        {{ props.label }}
      </slot>
    </view>
    ：
    <view :class="props.valueClass" flex-1 color="#303133">
      <slot />
      <slot name="value">
        {{ props.value }}
      </slot>
    </view>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
