<script lang="ts" setup>
defineOptions({
  name: 'Card',
})

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  theme: 'primary',
})

const emit = defineEmits(['on-click'])

interface Props {
  title?: string
  titleClass?: string
  size?: 'small' | 'medium'
  theme?: 'primary' | 'success'
}

const getCardClass = computed(() => {
  let result = 'bg-white'
  if (props.size === 'medium') {
    result += ' m-20rpx p-16rpx'
  }
  else if (props.size === 'small') {
    result += ' m-0'
  }
  return result
})

const getTitleClass = computed(() => {
  let result = ''
  if (props.size === 'medium') {
    result += ' px-12rpx py-16rpx text-32rpx'
  }
  else if (props.size === 'small') {
    result += ' p-x-12rpx p-y-8rpx text-24rpx'
  }

  if (props.theme === 'primary') {
    result += ' bg-[rgba(0,133,208,0.15)]'
  }
  else if (props.theme === 'success') {
    result += ' bg-#EFF6DE'
  }
  return result
})

function handleClick() {
  emit('on-click')
}
</script>

<template>
  <view class="component-card" :class="getCardClass" @click="handleClick">
    <view class="card-title" :class="[getTitleClass, props.titleClass]">
      <slot name="title">
        {{ title }}
      </slot>
    </view>
    <view class="card-content">
      <slot />
    </view>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
