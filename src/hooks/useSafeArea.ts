/**
 * 安全区域信息接口
 */
export interface SafeAreaInfo {
  top: number
  right: number
  bottom: number
  left: number
}

/**
 * 系统信息接口
 */
export interface SystemInfo {
  windowWidth: number
  windowHeight: number
  safeArea?: {
    top: number
    right: number
    bottom: number
    left: number
  }
  safeAreaInsets?: SafeAreaInfo
}

/**
 * 安全区域 Hook
 * 获取屏幕边界到安全区域的距离
 */
export function useSafeArea() {
  const safeAreaInsets = ref<SafeAreaInfo | null>(null)
  const systemInfo = ref<SystemInfo | null>(null)

  /**
   * 获取安全区域信息
   */
  const getSafeAreaInfo = () => {
    try {
      let info: SystemInfo

      // #ifdef MP-WEIXIN
      // 微信小程序使用新的API
      info = uni.getWindowInfo() as SystemInfo
      if (info.safeArea) {
        safeAreaInsets.value = {
          top: info.safeArea.top,
          right: info.windowWidth - info.safeArea.right,
          bottom: info.windowHeight - info.safeArea.bottom,
          left: info.safeArea.left,
        }
      }
      // #endif

      // #ifndef MP-WEIXIN
      // 其他平台继续使用uni API
      info = uni.getSystemInfoSync() as SystemInfo
      safeAreaInsets.value = info.safeAreaInsets || null
      // #endif

      systemInfo.value = info
    }
    catch (error) {
      console.error('获取安全区域信息失败:', error)
    }
  }

  /**
   * 获取顶部安全区域高度
   */
  const getTopSafeArea = () => {
    return safeAreaInsets.value?.top || 0
  }

  /**
   * 获取底部安全区域高度
   */
  const getBottomSafeArea = () => {
    return safeAreaInsets.value?.bottom || 0
  }

  /**
   * 获取左侧安全区域宽度
   */
  const getLeftSafeArea = () => {
    return safeAreaInsets.value?.left || 0
  }

  /**
   * 获取右侧安全区域宽度
   */
  const getRightSafeArea = () => {
    return safeAreaInsets.value?.right || 0
  }

  /**
   * 是否有刘海屏（顶部安全区域大于0）
   */
  const hasNotch = () => {
    return (safeAreaInsets.value?.top || 0) > 0
  }

  /**
   * 是否有底部安全区域（如iPhone的Home指示器）
   */
  const hasBottomSafeArea = () => {
    return (safeAreaInsets.value?.bottom || 0) > 0
  }

  // 组件挂载时获取安全区域信息
  onMounted(() => {
    getSafeAreaInfo()
  })

  return {
    /** 安全区域信息 */
    safeAreaInsets,
    /** 系统信息 */
    systemInfo,
    /** 手动获取安全区域信息 */
    getSafeAreaInfo,
    /** 获取顶部安全区域高度 */
    getTopSafeArea,
    /** 获取底部安全区域高度 */
    getBottomSafeArea,
    /** 获取左侧安全区域宽度 */
    getLeftSafeArea,
    /** 获取右侧安全区域宽度 */
    getRightSafeArea,
    /** 是否有刘海屏 */
    hasNotch,
    /** 是否有底部安全区域 */
    hasBottomSafeArea,
  }
}
