// 测试用的 iconfont，可生效
// @import './iconfont.css';

.test {
  // 可以通过 @apply 多个样式封装整体样式
  @apply mt-4 ml-4;

  padding-top: 4px;
  color: red;
}

:root,
page {
  // 修改按主题色
  // --wot-color-theme: #37c2bc;

  // 修改按钮背景色
  // --wot-button-primary-bg-color: green;

  // 以下为模块变量定义
  // 仅限在当前样式文件中使用
  // 请勿在其他文件中引用这些变量
  // 变量名来源ui设计师规范
  --default-text-primary-T1: #303133;
  --default-primary-color: #0085d0;

  /* --- */

  /* 主题颜色 */
  --wot-color-theme: var(--default-primary-color);

  /* input */
  --wot-input-bg: #fff;

  /* card */
  --wot-card-content-color: var(--default-text-primary-T1);

  background-color: #ededed;
}

/* 全局作用域 class */
.global-bg-image-bg {
  background-image: url('/static/images/bg.png');
  @apply bg-cover bg-no-repeat;
}

/* 铺盖默认的wot-ui 组件样式 */
// .wd-input {

// }
