import { http } from '@/http/http'

// 通知列表查询参数接口
export interface NoticeListQuery {
  /* 页码 */
  pageNum?: number
  /* 每页数量 */
  pageSize?: number
  /* 通知ID */
  noticeId?: number
  /* 通知标题 */
  noticeTitle?: string
  /* 通知内容 */
  noticeContent?: string
  /* 通知类型（1任务通知; 2审批通知; 3结果通知） */
  noticeType?: NoticeType
  /* 通知状态（0正常; 1关闭） */
  status?: NoticeStatus
  /* 是否已读（0未读; 1已读） */
  isRead?: NoticeReadStatus
  /* 用户ID */
  userId?: number
  /* 创建者 */
  createBy?: string
  /* 更新者 */
  updateBy?: string
  /* 更新时间 */
  updateTime?: string
  /* 排序的方式 (desc 或者 asc) */
  isAsc?: 'desc' | 'asc'
  /* 排序字段 */
  orderByColumn?: string
}

// 通知列表分页响应接口
export interface NoticeListPageResult {
  rows: NoticeItem[]
  total: number
}

// 获取通知列表接口
export function getNoticeList(query: NoticeListQuery) {
  return http<NoticeListPageResult>({
    url: '/system/notice/list',
    method: 'GET',
    query,
  })
}

// 标记通知为已读接口
export function markNoticeAsRead(noticeId: number) {
  return http<string>({
    url: `/system/notice/read/${noticeId}`,
    method: 'POST',
  })
}

// 获取通知详情接口
export function getNoticeDetail(noticeId: number) {
  return http<NoticeItem>({
    url: `/system/notice/detail/${noticeId}`,
    method: 'GET',
  })
}
