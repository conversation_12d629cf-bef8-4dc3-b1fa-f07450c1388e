import { http } from '@/http/http'

export interface ProjectInfoQuery {
  /* 排序的方式 (desc 或者 asc) */
  isAsc?: 'desc' | 'asc'
  /* 排序字段 */
  orderByColumn?: string
  /* 页码 */
  pageNum?: number
  /* 每页数量 */
  pageSize?: number
  /* 项目状态 */
  projectStatus?: ProjectStatusValue
  /* 机房地址 */
  roomAddress?: string
  /* 机房编号 */
  roomCode?: string
  /* 机房名称 */
  roomName?: string
  /* 机房类型 */
  roomType?: string
}

// 项目信息分页响应接口
export interface ProjectInfoPageResult {
  list: ProjectInfoItem[]
  total: number
}

// 数据看板列表接口
export function projectInfo(query: ProjectInfoQuery) {
  return http<ProjectInfoPageResult>({
    url: `/project/project_info/page`,
    method: 'GET',
    query,
  })
}

// 新增项目接口
export function projectAdd(data: any) {
  return http<any>({
    url: `/project/project_info/add`,
    method: 'POST',
    data,
  })
}

// 项目详情接口
export function projectDetail(id: string) {
  return http<ProjectDetailResponse>({
    url: `/project/project_info/detail`,
    method: 'GET',
    query: { id },
  })
}

// 更新项目接口
export function projectUpdate(data: ProjectInfoItem) {
  return http<string>({
    url: `/project/project_info/update`,
    method: 'PUT',
    data,
  })
}

// 删除项目接口
export function projectDelete(id: string) {
  return http<string>({
    url: `/project/project_info/delete/${id}`,
    method: 'DELETE',
  })
}
