{"name": "走线架AI识别工具", "appid": "__UNI__7B0765D", "description": "", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "packageName": "com.ai.cable.rjgf", "minSdkVersion": 30, "targetSdkVersion": 30, "abiFilters": ["armeabi-v7a", "arm64-v8a"]}, "ios": {}, "sdkConfigs": {}, "icons": {"android": {"hdpi": "static/app/icons/72x72.png", "xhdpi": "static/app/icons/96x96.png", "xxhdpi": "static/app/icons/144x144.png", "xxxhdpi": "static/app/icons/192x192.png"}, "ios": {"appstore": "static/app/icons/1024x1024.png", "ipad": {"app": "static/app/icons/76x76.png", "app@2x": "static/app/icons/152x152.png", "notification": "static/app/icons/20x20.png", "notification@2x": "static/app/icons/40x40.png", "proapp@2x": "static/app/icons/167x167.png", "settings": "static/app/icons/29x29.png", "settings@2x": "static/app/icons/58x58.png", "spotlight": "static/app/icons/40x40.png", "spotlight@2x": "static/app/icons/80x80.png"}, "iphone": {"app@2x": "static/app/icons/120x120.png", "app@3x": "static/app/icons/180x180.png", "notification@2x": "static/app/icons/40x40.png", "notification@3x": "static/app/icons/60x60.png", "settings@2x": "static/app/icons/58x58.png", "settings@3x": "static/app/icons/87x87.png", "spotlight@2x": "static/app/icons/80x80.png", "spotlight@3x": "static/app/icons/120x120.png"}}}}, "darkmode": false, "compatible": {"ignoreVersion": true}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false, "es6": true, "minified": true}, "usingComponents": true, "optimization": {"subPackages": true}}, "mp-alipay": {"usingComponents": true, "styleIsolation": "shared"}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3", "h5": {"router": {}}}