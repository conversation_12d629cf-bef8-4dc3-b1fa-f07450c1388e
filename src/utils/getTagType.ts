import type { TagType } from 'wot-design-uni/components/wd-tag/types'

// 获取机房类型标签样式
export function getRoomTagType(type: string): TagType {
  return 'primary'
}

// 获取任务状态标签样式
export function getStatusTagType(status: string): TagType {
  switch (status) {
    case '图像待提交':
      return 'warning'
    case 'AI识别待确认':
      return 'default'
    case '已结束':
      return 'success'
    default:
      return 'warning'
  }
}
