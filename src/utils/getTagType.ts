import type { TagType } from 'wot-design-uni/components/wd-tag/types'

// 获取机房类型标签样式
export function getRoomTagType(type: string): TagType {
  return 'primary'
}

// 获取任务状态标签样式
export function getStatusTagType(status: string): TagType {
  switch (status) {
    case '图像待提交':
      return 'warning'
    case 'AI识别待确认':
      return 'default'
    case '已结束':
      return 'success'
    default:
      return 'warning'
  }
}

// 获取存在性/符合性标签样式
export function getExistTagType(text?: string): TagType {
  if (!text)
    return 'warning'
  if (text.includes('符合') || text.includes('存在'))
    return 'success'
  if (text.includes('不符合') || text.includes('不存在'))
    return 'danger'
  return 'warning'
}

// 获取项目状态标签样式（通用）
export function getProjectTagType(_projectStatus: string): TagType {
  return 'warning'
}

// 获取合规性信息（包含文本和标签类型）
export function getComplianceByNoQualified(noQualified?: string) {
  const ok = !noQualified || noQualified.trim() === ''
  return {
    text: ok ? '符合规范' : '不符合规范',
    type: (ok ? 'success' : 'error') as TagType,
  }
}
