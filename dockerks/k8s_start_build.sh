#!/bin/bash

# 错误退出设置
set -e
# 调用 IMAGES_NAME=1 #给变量赋值
#     export IMAGES_NAME #设置全局临时变量
# 参数名称如下
# IMAGES_NAME 镜像名称
# DOCKER_FILE_NAME docker文件名称
# DOCKERHUB_URL docker仓库地址 默认填 192.168.10.102:8110
# DOCKERHUB_USERNAME docker仓库地址
# DOCKERHUB_PASSWORD docker仓库地址
# NAME_SPACE 项目空间
# BUILD_ENV build环境 默认填 test


images_name=$IMAGES_NAME
if [ ! $images_name ]; then
    echo "IMAGES_NAME is blank"
    exit;
fi

docker_file_name=$DOCKER_FILE_NAME
if [ ! $docker_file_name ]; then
    echo "DOCKER_FILE_NAME is blank"
    exit;
fi

dockerhub_url=$DOCKERHUB_URL
if [ ! $dockerhub_url ]; then
    dockerhub_url='192.168.10.102:8110'
fi

build_env=$BUILD_ENV
if [ ! $build_env ]; then
  build_env='test'
fi

dockerhub_username=$DOCKERHUB_USERNAME
if [ ! $dockerhub_username ]; then
     echo "DOCKERHUB_USERNAME is blank"
     exit;
fi

dockerhub_password=$DOCKERHUB_PASSWORD
if [ ! $dockerhub_password ]; then
     echo "DOCKERHUB_PASSWORD is blank"
     exit;
fi

tag_name=$TAG_NAME
if [ ! $tag_name ]; then
     tag_name='temp'
fi

name_space=$NAME_SPACE
if [ ! $name_space ]; then
     name_space='temp'
fi


######################################根据各项目构建情况酌情修改,#################################################
cd ../
echo "install pnpm@10.10.0"
npm install -g pnpm@10.10.0
echo "pnpm --version"
pnpm --version
pnpm install  --unsafe-perm=true
pnpm run build:$build_env
cd dockerks


echo "move dist to dockerfile dir"
rm -rf ./dockerfile/dist
cp -r ../dist/ ./dockerfile/dist

#######################################################################################

echo "clear images"
imageid=$(docker images | grep "$images_name" | awk '{print $3}')
if [ "$imageid" != "" ]; then
   docker images | grep "$images_name" | awk '{print $3}'|xargs docker rmi || true
   echo "clear images finish"
fi


cd dockerfile
echo "create images"
docker build -t $dockerhub_url/k8s/$images_name:latest -f $docker_file_name .
cd ..

echo "tag images"
docker tag $dockerhub_url/k8s/$images_name:latest $dockerhub_url/k8s/$images_name:$tag_name


echo "login docker"
docker login -u $dockerhub_username -p $dockerhub_password $dockerhub_url

echo "push latest images "
docker push $dockerhub_url/k8s/$images_name:latest

echo "push tag images "
docker push $dockerhub_url/k8s/$images_name:$tag_name



echo "covert docker-compose.yml"
kompose convert

echo "deploy to K8S"

#持久卷发布
list=$(find ./ -name $images_name'-claim*-persistentvolumeclaim.yaml')
for file_name in $list
do
      #添加发布空间
      line_num=$(cat $file_name |grep -n -w "^metadata:"|grep -oE '^\s*[0-9]+')
      let "target_num = $line_num+1"
      sed -i $target_num'i\ \ namespace:\ '$name_space $file_name
      cat $file_name
      #执行发布指令
      kubectl apply -f $file_name
done

#工作负载发布
deploy_file=$images_name-deployment.yaml

if [ -e $deploy_file ]; then
      #添加发布空间
      line_num=$(cat $deploy_file |grep -n -w "^metadata:"|grep -oE '^\s*[0-9]+')
      let "target_num = $line_num+1"
      sed -i $target_num'i\ \ namespace:\ '$name_space $deploy_file
      #添加随机数
      line_num_two=$(cat $deploy_file |grep -n -w "^      creationTimestamp:"|grep -oE '^\s*[0-9]+')
      current_time=$(date "+%Y-%m-%dT%H:%M:%SZ")
      sed -i $line_num_two'c\ \ \ \ \ \ creationTimestamp: "'$current_time'"' $deploy_file
      cat $deploy_file
      #执行发布指令
      kubectl apply -f $deploy_file
fi

#服务发布
service_file=$images_name-service.yaml

if [ -e $service_file ]; then
      #添加发布空间
      line_num=$(cat $service_file |grep -n -w "^metadata:"|grep -oE '^\s*[0-9]+')
      let "target_num = $line_num+1"
      sed -i $target_num'i\ \ namespace:\ '$name_space $service_file
      cat $service_file
      #执行发布指令
      kubectl apply -f $service_file
fi
